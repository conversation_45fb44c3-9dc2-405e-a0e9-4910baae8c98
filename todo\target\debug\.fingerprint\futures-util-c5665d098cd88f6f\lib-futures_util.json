{"rustc": 1842507548689473721, "features": "[\"alloc\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 17082804440428533908, "deps": [[5103565458935487, "futures_io", false, 7007063211124538598], [1615478164327904835, "pin_utils", false, 17289683164583425804], [1811549171721445101, "futures_channel", false, 8161504045738334901], [1906322745568073236, "pin_project_lite", false, 2316922201855175487], [7013762810557009322, "futures_sink", false, 10986586839717050596], [7620660491849607393, "futures_core", false, 6799085311733312643], [14767213526276824509, "slab", false, 16057578451457990816], [15932120279885307830, "memchr", false, 16448635728646224783], [16240732885093539806, "futures_task", false, 9272982973381587285]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-c5665d098cd88f6f\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}