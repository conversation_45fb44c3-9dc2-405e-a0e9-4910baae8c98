{"rustc": 1842507548689473721, "features": "[\"futures-sink\", \"sink\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8113656176662020586, "path": 17082804440428533908, "deps": [[1615478164327904835, "pin_utils", false, 17289683164583425804], [1906322745568073236, "pin_project_lite", false, 2316922201855175487], [7013762810557009322, "futures_sink", false, 16584038885035085768], [7620660491849607393, "futures_core", false, 4429178334248123100], [16240732885093539806, "futures_task", false, 4142774259235197544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-18fa4c4673bad0df\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}